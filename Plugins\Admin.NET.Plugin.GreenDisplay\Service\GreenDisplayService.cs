// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Plugin.GreenDisplay.Options;
using Admin.NET.Plugin.GreenDisplay.Exceptions;
using Furion.DependencyInjection;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Collections.Concurrent;
using Admin.NET.Plugin.GreenDisplay.Dto;

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 蓝牙桌牌服务
/// </summary>
public class GreenDisplayService : ITransient
{
    private readonly IGreenDisplayApi _api;
    private readonly GreenDisplayOptions _options;
    private readonly IMemoryCache _cache;
    private readonly ILogger<GreenDisplayService> _logger;
    private readonly SemaphoreSlim _tokenRefreshSemaphore;
    private const string TOKEN_CACHE_KEY = "GreenDisplay_AccessToken";

    public GreenDisplayService(
        IGreenDisplayApi api,
        IOptions<GreenDisplayOptions> options,
        IMemoryCache cache,
        ILogger<GreenDisplayService> logger)
    {
        _api = api;
        _options = options.Value;
        _cache = cache;
        _logger = logger;
        _tokenRefreshSemaphore = new SemaphoreSlim(1, 1);
    }

    #region 认证管理

    /// <summary>
    /// 获取访问令牌（支持自动重试和刷新）
    /// </summary>
    /// <param name="forceRefresh">是否强制刷新令牌</param>
    /// <returns>访问令牌</returns>
    public async Task<string> GetAccessTokenAsync(bool forceRefresh = false)
    {
        // 验证配置
        if (string.IsNullOrEmpty(_options.Username) || string.IsNullOrEmpty(_options.Password))
        {
            throw new GreenDisplayConfigException("GreenDisplay用户名或密码未配置");
        }

        // 如果不强制刷新，尝试从缓存获取令牌
        if (!forceRefresh && _cache.TryGetValue(TOKEN_CACHE_KEY, out string cachedToken) && !string.IsNullOrEmpty(cachedToken))
        {
            return cachedToken;
        }

        // 使用信号量确保同时只有一个线程在刷新令牌
        await _tokenRefreshSemaphore.WaitAsync();
        try
        {
            // 双重检查：可能其他线程已经刷新了令牌
            if (!forceRefresh && _cache.TryGetValue(TOKEN_CACHE_KEY, out string doubleCheckedToken) && !string.IsNullOrEmpty(doubleCheckedToken))
            {
                return doubleCheckedToken;
            }

            return await RefreshTokenAsync();
        }
        finally
        {
            _tokenRefreshSemaphore.Release();
        }
    }

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    /// <returns>新的访问令牌</returns>
    private async Task<string> RefreshTokenAsync()
    {
        try
        {
            var loginInput = new GreenDisplayLoginInput
            {
                Username = _options.Username,
                Password = _options.Password
            };

            var response = await _api.LoginAsync(loginInput);
            
            if (response?.Success == true && response.Data != null && !string.IsNullOrEmpty(response.Data.AccessToken))
            {
                var token = response.Data.AccessToken;
                
                // 缓存令牌，设置过期时间（提前5分钟过期以避免边界情况）
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.CacheExpirationMinutes - 5)
                };
                _cache.Set(TOKEN_CACHE_KEY, token, cacheOptions);
                
                _logger.LogInformation("GreenDisplay访问令牌刷新成功");
                return token;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogError("GreenDisplay登录失败：{Message}", errorMessage);
                throw new GreenDisplayAuthException($"登录失败：{errorMessage}");
            }
        }
        catch (GreenDisplayAuthException)
        {
            throw;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "网络请求异常，获取GreenDisplay访问令牌失败");
            throw new GreenDisplayNetworkException("网络连接失败，无法获取访问令牌", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "请求超时，获取GreenDisplay访问令牌失败");
            throw new GreenDisplayNetworkException("请求超时，无法获取访问令牌", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取GreenDisplay访问令牌时发生未知异常");
            throw new GreenDisplayException("UNKNOWN_ERROR", "获取访问令牌失败", ex);
        }
    }

    /// <summary>
    /// 清除缓存的令牌
    /// </summary>
    public void ClearCachedToken()
    {
        _cache.Remove(TOKEN_CACHE_KEY);
        _logger.LogInformation("GreenDisplay访问令牌缓存已清除");
    }

    /// <summary>
    /// 执行带重试机制的操作
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称（用于日志）</param>
    /// <param name="maxRetries">最大重试次数</param>
    /// <returns>操作结果</returns>
    private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, int maxRetries = -1)
    {
        if (maxRetries == -1)
        {
            maxRetries = _options.RetryCount;
        }

        Exception lastException = null;
        
        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                return await operation();
            }
            catch (GreenDisplayAuthException ex) when (attempt < maxRetries)
            {
                _logger.LogWarning("认证失败，尝试刷新令牌并重试 {OperationName}，第 {Attempt}/{MaxRetries} 次", operationName, attempt + 1, maxRetries + 1);
                
                // 清除缓存的令牌并强制刷新
                ClearCachedToken();
                lastException = ex;
                
                // 等待一段时间后重试
                await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt)));
            }
            catch (GreenDisplayNetworkException ex) when (attempt < maxRetries)
            {
                _logger.LogWarning("网络异常，重试 {OperationName}，第 {Attempt}/{MaxRetries} 次：{Message}", operationName, attempt + 1, maxRetries + 1, ex.Message);
                lastException = ex;
                
                // 指数退避重试
                await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行 {OperationName} 时发生异常，第 {Attempt}/{MaxRetries} 次", operationName, attempt + 1, maxRetries + 1);
                lastException = ex;
                
                if (attempt == maxRetries)
                {
                    break;
                }
                
                // 对于其他异常，也进行重试但间隔更短
                await Task.Delay(TimeSpan.FromSeconds(1));
            }
        }
        
        _logger.LogError("执行 {OperationName} 失败，已达到最大重试次数 {MaxRetries}", operationName, maxRetries + 1);
        throw lastException ?? new GreenDisplayException("RETRY_EXHAUSTED", $"执行 {operationName} 失败，已达到最大重试次数");
    }

    #endregion

    #region 设备管理

    /// <summary>
    /// 绑定多个标签（支持自动重试）
    /// </summary>
    /// <param name="input">绑定信息</param>
    /// <returns>绑定结果</returns>
    public async Task<bool> BindMultipleLabelsAsync(BindMultipleLabelsInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.LabelMac))
        {
            throw new GreenDisplayDeviceException("标签MAC地址不能为空", input.LabelMac);
        }

        return await ExecuteWithRetryAsync(async () =>
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.BindMultipleLabelsAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("成功绑定标签，MAC：{Mac}", input.LabelMac);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("绑定标签失败：{Message}，MAC：{Mac}", errorMessage, input.LabelMac);
                throw new GreenDisplayDeviceException($"绑定标签失败：{errorMessage}", input.LabelMac);
            }
        }, $"绑定标签 {input.LabelMac}");
    }

    /// <summary>
    /// 获取设备信息（支持自动重试）
    /// </summary>
    /// <param name="mac">设备MAC地址</param>
    /// <returns>设备信息</returns>
    public async Task<DeviceInfo> GetDeviceAsync(string mac)
    {
        if (string.IsNullOrEmpty(mac))
        {
            throw new GreenDisplayDeviceException("设备MAC地址不能为空", mac);
        }

        return await ExecuteWithRetryAsync(async () =>
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.GetDeviceAsync(mac, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                return response.Data;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("获取设备信息失败：{Message}，MAC：{Mac}", errorMessage, mac);
                throw new GreenDisplayDeviceException($"获取设备信息失败：{errorMessage}", mac);
            }
        }, $"获取设备信息 {mac}");
    }

    /// <summary>
    /// 查询设备列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>设备列表</returns>
    public async Task<PagedResponse<DeviceInfo>> QueryDevicesAsync(QueryDevicesInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.QueryDevicesAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                return response.Data ?? new PagedResponse<DeviceInfo> { list = new List<DeviceInfo>() };
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("查询设备列表失败：{Message}", errorMessage);
                return new PagedResponse<DeviceInfo> { list = new List<DeviceInfo>() };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询设备列表时发生异常");
            return new PagedResponse<DeviceInfo> { list = new List<DeviceInfo>() };
        }
    }

    /// <summary>
    /// 绑定标签数据
    /// </summary>
    /// <param name="input">绑定数据信息</param>
    /// <returns>绑定结果</returns>
    public async Task<bool> BindDataMultipleAsync(BindDataMultipleInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.BindDataMultipleAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("成功绑定标签数据");
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("绑定标签数据失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "绑定标签数据时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 闪烁标签
    /// </summary>
    /// <param name="mac">设备MAC地址</param>
    /// <param name="duration">闪烁时长（秒）</param>
    /// <returns>操作结果</returns>
    public async Task<bool> FlashLabelAsync(string mac, int duration = 5)
    {
        if (string.IsNullOrEmpty(mac))
        {
            throw new ArgumentException("设备MAC地址不能为空", nameof(mac));
        }
        
        if (duration <= 0)
        {
            throw new ArgumentException("闪烁时长必须大于0", nameof(duration));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var input = new FlashLabelInput
            {
                LabelMac = mac,
                Time = duration
            };
            
            var response = await _api.FlashLabelAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("标签闪烁命令发送成功，MAC：{Mac}", mac);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("标签闪烁命令发送失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送标签闪烁命令时发生异常，MAC：{Mac}", mac);
            return false;
        }
    }

    /// <summary>
    /// 更新标签
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateLabelAsync(UpdateLabelInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.LabelMac))
        {
            throw new ArgumentException("标签MAC地址不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.UpdateLabelAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("标签更新成功，MAC：{Mac}", input.LabelMac);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("标签更新失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新标签时发生异常，MAC：{Mac}", input?.LabelMac);
            return false;
        }
    }

    #endregion

    #region 会议管理

    /// <summary>
    /// 创建会议室
    /// </summary>
    /// <param name="input">会议室信息</param>
    /// <returns>创建结果</returns>
    public async Task<int> CreateMeetingRoomAsync(CreateMeetingRoomInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.Name))
        {
            throw new ArgumentException("会议室名称不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.CreateMeetingRoomAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                int thirdPartyId = Convert.ToInt32(response.Data);
                _logger.LogInformation("会议室创建成功：{Name}，第三方平台ID：{ThirdPartyId}", input.Name, thirdPartyId);
                return thirdPartyId;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("会议室创建失败：{Message}", errorMessage);
                return 0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建会议室时发生异常，名称：{Name}", input?.Name);
            return 0;
        }
    }

    /// <summary>
    /// 查询会议室列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>会议室列表</returns>
    public async Task<PagedResponse<MeetingRoomInfo>> QueryMeetingRoomsAsync(QueryMeetingRoomsInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.QueryMeetingRoomsAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                return response.Data ?? new PagedResponse<MeetingRoomInfo> { list = new List<MeetingRoomInfo>() };
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("查询会议室列表失败：{Message}", errorMessage);
                return new PagedResponse<MeetingRoomInfo> { list = new List<MeetingRoomInfo>() };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询会议室列表时发生异常");
            return new PagedResponse<MeetingRoomInfo> { list = new List<MeetingRoomInfo>() };
        }
    }

    /// <summary>
    /// 更新会议室
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateMeetingRoomAsync(UpdateMeetingRoomInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.Name))
        {
            throw new ArgumentException("会议室名称不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.UpdateMeetingRoomAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("会议室更新成功：{Name}", input.Name);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("会议室更新失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新会议室时发生异常，名称：{Name}", input?.Name);
            return false;
        }
    }

    /// <summary>
    /// 删除会议室
    /// </summary>
    /// <param name="id">会议室ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteMeetingRoomAsync(int id)
    {
        if (id <= 0)
        {
            throw new ArgumentException("会议室ID必须大于0", nameof(id));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.DeleteMeetingRoomAsync(id, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("会议室删除成功，ID：{Id}", id);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("会议室删除失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除会议室时发生异常，ID：{Id}", id);
            return false;
        }
    }

    /// <summary>
    /// 创建会议人员
    /// </summary>
    /// <param name="input">人员信息</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateMeetingStaffAsync(CreateMeetingStaffInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.Name))
        {
            throw new ArgumentException("人员姓名不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.CreateMeetingStaffAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("会议人员创建成功：{Name}", input.Name);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("会议人员创建失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建会议人员时发生异常，姓名：{Name}", input?.Name);
            return false;
        }
    }

    /// <summary>
    /// 查询会议人员
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>人员列表</returns>
    public async Task<PagedResponse<MeetingStaffInfo>> QueryMeetingStaffAsync(QueryMeetingStaffInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.QueryMeetingStaffAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                return response.Data ?? new PagedResponse<MeetingStaffInfo> { list = new List<MeetingStaffInfo>() };
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("查询会议人员失败：{Message}", errorMessage);
                return new PagedResponse<MeetingStaffInfo> { list = new List<MeetingStaffInfo>() };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询会议人员时发生异常");
            return new PagedResponse<MeetingStaffInfo> { list = new List<MeetingStaffInfo>() };
        }
    }

    /// <summary>
    /// 更新会议人员
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateMeetingStaffAsync(UpdateMeetingStaffInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.Name))
        {
            throw new ArgumentException("人员姓名不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.UpdateMeetingStaffAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("会议人员更新成功：{Name}", input.Name);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("会议人员更新失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新会议人员时发生异常，姓名：{Name}", input?.Name);
            return false;
        }
    }

    /// <summary>
    /// 删除会议人员
    /// </summary>
    /// <param name="id">人员ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteMeetingStaffAsync(int id)
    {
        if (id <= 0)
        {
            throw new ArgumentException("人员ID必须大于0", nameof(id));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.DeleteMeetingStaffAsync(id, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("会议人员删除成功，ID：{Id}", id);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("会议人员删除失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除会议人员时发生异常，ID：{Id}", id);
            return false;
        }
    }

    #endregion

    #region 模板管理

    /// <summary>
    /// 查询模板列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>模板列表</returns>
    public async Task<PagedResponse<TemplateOutput>> QueryTemplatesAsync(QueryTemplatesInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.QueryTemplatesAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                return response.Data ?? new PagedResponse<TemplateOutput> { list = new List<TemplateOutput>() };
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("查询模板列表失败：{Message}", errorMessage);
                return new PagedResponse<TemplateOutput> { list = new List<TemplateOutput>() };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询模板列表时发生异常");
            return new PagedResponse<TemplateOutput> { list = new List<TemplateOutput>() };
        }
    }

    /// <summary>
    /// 创建模板
    /// </summary>
    /// <param name="input">模板信息</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateTemplateAsync(CreateTemplateInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.TemplateName))
        {
            throw new ArgumentException("模板名称不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.CreateTemplateAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("模板创建成功：{Name}", input.TemplateName);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("模板创建失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建模板时发生异常，名称：{Name}", input?.TemplateName);
            return false;
        }
    }

    #endregion

    #region AP管理

    /// <summary>
    /// 获取AP列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>AP列表</returns>
    public async Task<PagedResponse<APOutput>> GetAPListAsync(QueryAPInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.GetAPListAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                return response.Data ?? new PagedResponse<APOutput> { list = new List<APOutput>() };
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("获取AP列表失败：{Message}", errorMessage);
                return new PagedResponse<APOutput> { list = new List<APOutput>() };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AP列表时发生异常");
            return new PagedResponse<APOutput> { list = new List<APOutput>() };
        }
    }

    /// <summary>
    /// 创建网关
    /// </summary>
    /// <param name="input">网关信息</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateAPAsync(CreateAPInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.ApName))
        {
            throw new ArgumentException("网关名称不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.CreateAPAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("网关创建成功：{Name}", input.ApName);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("网关创建失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建网关时发生异常，名称：{Name}", input?.ApName);
            return false;
        }
    }

    /// <summary>
    /// 根据网关MAC获取网关基本信息
    /// </summary>
    /// <param name="apMac">网关MAC地址</param>
    /// <returns>网关信息</returns>
    public async Task<APOutput> GetAPAsync(string apMac)
    {
        if (string.IsNullOrEmpty(apMac))
        {
            throw new ArgumentException("网关MAC地址不能为空", nameof(apMac));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.GetAPAsync(apMac, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                return response.Data;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("获取网关信息失败：{Message}", errorMessage);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取网关信息时发生异常，MAC：{Mac}", apMac);
            return null;
        }
    }

    /// <summary>
    /// 修改网关信息
    /// </summary>
    /// <param name="input">更新信息</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAPAsync(UpdateAPInput input)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }
        
        if (string.IsNullOrEmpty(input.ApName))
        {
            throw new ArgumentException("网关名称不能为空", nameof(input));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.UpdateAPAsync(input, $"Bearer {token}");
            
            if (response?.Success == true)
            {
                _logger.LogInformation("网关更新成功：{Name}", input.ApName);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("网关更新失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新网关时发生异常，名称：{Name}", input?.ApName);
            return false;
        }
    }

    /// <summary>
    /// 删除网关
    /// </summary>
    /// <param name="apMac">网关MAC地址</param>
    /// <returns>删除结果</returns>
    /// <exception cref="ArgumentException"></exception>
    public async Task<bool> DeleteAPAsync(string apMac)
    {
        if (string.IsNullOrEmpty(apMac))
        {
            throw new ArgumentException("网关MAC地址不能为空", nameof(apMac));
        }

        try
        {
            var token = await GetAccessTokenAsync();
            var response = await _api.DeleteAPAsync(apMac, $"Bearer {token}");

            if (response?.Success == true)
            {
                _logger.LogInformation("网关删除成功：{Mac}", apMac);
                return true;
            }
            else
            {
                var errorMessage = response?.msg ?? "未知错误";
                _logger.LogWarning("网关删除失败：{Message}", errorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除网关时发生异常，MAC：{Mac}", apMac);
            return false;
        }
    }

    #endregion

    #region 批量操作

    /// <summary>
    /// 批量绑定设备
    /// </summary>
    /// <param name="input">批量绑定输入</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量操作结果</returns>
    public async Task<BatchOperationResult<BindMultipleLabelsInput>> BatchBindDevicesAsync(BatchDeviceBindInput input, CancellationToken cancellationToken = default)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        if (input.DeviceBindings == null || !input.DeviceBindings.Any())
        {
            throw new ArgumentException("设备绑定列表不能为空", nameof(input));
        }

        var result = new BatchOperationResult<BindMultipleLabelsInput>
        {
            Total = input.DeviceBindings.Count
        };

        _logger.LogInformation("开始批量绑定设备，总数：{Count}，并发限制：{Limit}", input.DeviceBindings.Count, input.ConcurrencyLimit);

        using var semaphore = new SemaphoreSlim(input.ConcurrencyLimit, input.ConcurrencyLimit);
        var tasks = input.DeviceBindings.Select(async binding =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var success = await BindMultipleLabelsAsync(binding);
                if (success)
                {
                    lock (result)
                    {
                        result.SuccessCount++;
                        result.SuccessItems.Add(binding);
                    }
                }
                else
                {
                    lock (result)
                    {
                        result.FailureCount++;
                        result.FailureItems.Add(new BatchOperationError<BindMultipleLabelsInput>
                        {
                            Item = binding,
                            ErrorMessage = "绑定失败",
                            ErrorCode = "BIND_FAILED"
                        });
                    }
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "批量绑定设备时发生异常，MAC：{Mac}", binding?.LabelMac);
                
                lock (result)
                {
                    result.FailureCount++;
                    result.FailureItems.Add(new BatchOperationError<BindMultipleLabelsInput>
                    {
                        Item = binding,
                        ErrorMessage = ex.Message,
                        ErrorCode = ex is GreenDisplayException gdEx ? gdEx.ErrorCode : "UNKNOWN_ERROR",
                        Exception = ex
                    });
                }

                if (!input.ContinueOnError)
                {
                    throw;
                }
            }
            finally
            {
                semaphore.Release();
            }
        });

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (Exception) when (input.ContinueOnError)
        {
            // 继续处理错误时，忽略异常
        }

        _logger.LogInformation("批量绑定设备完成，成功：{Success}，失败：{Failure}，成功率：{Rate:P2}", 
            result.SuccessCount, result.FailureCount, result.SuccessRate);

        return result;
    }

    /// <summary>
    /// 批量查询设备信息
    /// </summary>
    /// <param name="input">批量查询输入</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量操作结果，包含设备信息字典</returns>
    public async Task<(BatchOperationResult<string> Result, ConcurrentDictionary<string, DeviceInfo> DeviceInfos)> BatchQueryDevicesAsync(BatchDeviceQueryInput input, CancellationToken cancellationToken = default)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        if (input.DeviceMacs == null || !input.DeviceMacs.Any())
        {
            throw new ArgumentException("设备MAC列表不能为空", nameof(input));
        }

        var result = new BatchOperationResult<string>
        {
            Total = input.DeviceMacs.Count
        };

        var deviceInfos = new ConcurrentDictionary<string, DeviceInfo>();

        _logger.LogInformation("开始批量查询设备信息，总数：{Count}，并发限制：{Limit}", input.DeviceMacs.Count, input.ConcurrencyLimit);

        using var semaphore = new SemaphoreSlim(input.ConcurrencyLimit, input.ConcurrencyLimit);
        var tasks = input.DeviceMacs.Select(async mac =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var deviceInfo = await GetDeviceAsync(mac);
                if (deviceInfo != null)
                {
                    deviceInfos.TryAdd(mac, deviceInfo);
                    lock (result)
                    {
                        result.SuccessCount++;
                        result.SuccessItems.Add(mac);
                    }
                }
                else
                {
                    lock (result)
                    {
                        result.FailureCount++;
                        result.FailureItems.Add(new BatchOperationError<string>
                        {
                            Item = mac,
                            ErrorMessage = "设备信息不存在或查询失败",
                            ErrorCode = "DEVICE_NOT_FOUND"
                        });
                    }
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "批量查询设备信息时发生异常，MAC：{Mac}", mac);
                
                lock (result)
                {
                    result.FailureCount++;
                    result.FailureItems.Add(new BatchOperationError<string>
                    {
                        Item = mac,
                        ErrorMessage = ex.Message,
                        ErrorCode = ex is GreenDisplayException gdEx ? gdEx.ErrorCode : "UNKNOWN_ERROR",
                        Exception = ex
                    });
                }

                if (!input.ContinueOnError)
                {
                    throw;
                }
            }
            finally
            {
                semaphore.Release();
            }
        });

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (Exception) when (input.ContinueOnError)
        {
            // 继续处理错误时，忽略异常
        }

        _logger.LogInformation("批量查询设备信息完成，成功：{Success}，失败：{Failure}，成功率：{Rate:P2}", 
            result.SuccessCount, result.FailureCount, result.SuccessRate);

        return (result, deviceInfos);
    }

    /// <summary>
    /// 批量更新标签
    /// </summary>
    /// <param name="input">批量更新输入</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量操作结果</returns>
    public async Task<BatchOperationResult<UpdateLabelInput>> BatchUpdateLabelsAsync(BatchLabelUpdateInput input, CancellationToken cancellationToken = default)
    {
        if (input == null)
        {
            throw new ArgumentNullException(nameof(input));
        }

        if (input.LabelUpdates == null || !input.LabelUpdates.Any())
        {
            throw new ArgumentException("标签更新列表不能为空", nameof(input));
        }

        var result = new BatchOperationResult<UpdateLabelInput>
        {
            Total = input.LabelUpdates.Count
        };

        _logger.LogInformation("开始批量更新标签，总数：{Count}，并发限制：{Limit}", input.LabelUpdates.Count, input.ConcurrencyLimit);

        using var semaphore = new SemaphoreSlim(input.ConcurrencyLimit, input.ConcurrencyLimit);
        var tasks = input.LabelUpdates.Select(async updateInput =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var success = await UpdateLabelAsync(updateInput);
                if (success)
                {
                    lock (result)
                    {
                        result.SuccessCount++;
                        result.SuccessItems.Add(updateInput);
                    }
                }
                else
                {
                    lock (result)
                    {
                        result.FailureCount++;
                        result.FailureItems.Add(new BatchOperationError<UpdateLabelInput>
                        {
                            Item = updateInput,
                            ErrorMessage = "更新失败",
                            ErrorCode = "UPDATE_FAILED"
                        });
                    }
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "批量更新标签时发生异常，标签ID：{LabelId}", updateInput?.LabelMac);
                
                lock (result)
                {
                    result.FailureCount++;
                    result.FailureItems.Add(new BatchOperationError<UpdateLabelInput>
                    {
                        Item = updateInput,
                        ErrorMessage = ex.Message,
                        ErrorCode = ex is GreenDisplayException gdEx ? gdEx.ErrorCode : "UNKNOWN_ERROR",
                        Exception = ex
                    });
                }

                if (!input.ContinueOnError)
                {
                    throw;
                }
            }
            finally
            {
                semaphore.Release();
            }
        });

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (Exception) when (input.ContinueOnError)
        {
            // 继续处理错误时，忽略异常
        }

        _logger.LogInformation("批量更新标签完成，成功：{Success}，失败：{Failure}，成功率：{Rate:P2}", 
            result.SuccessCount, result.FailureCount, result.SuccessRate);

        return result;
    }

    #endregion
}