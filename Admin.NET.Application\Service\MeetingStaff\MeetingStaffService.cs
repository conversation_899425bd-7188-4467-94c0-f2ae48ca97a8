// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Admin.NET.Application.Entity;
using Admin.NET.Plugin.GreenDisplay.Service;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;
using Yitter.IdGenerator;
namespace Admin.NET.Application;

/// <summary>
/// 会议人员表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class MeetingStaffService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MeetingStaff> _meetingStaffRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly GreenDisplayService _greenDisplayService;
    private readonly ILogger<MeetingStaffService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IDistributedCache _cache;
    private readonly SysCacheService _sysCacheService;

    public MeetingStaffService(SqlSugarRepository<MeetingStaff> meetingStaffRep, 
        ISqlSugarClient sqlSugarClient,
        GreenDisplayService greenDisplayService,
        ILogger<MeetingStaffService> logger,
        IConfiguration configuration,
        IDistributedCache cache,
        SysCacheService sysCacheService)
    {
        _meetingStaffRep = meetingStaffRep;
        _sqlSugarClient = sqlSugarClient;
        _greenDisplayService = greenDisplayService;
        _logger = logger;
        _configuration = configuration;
        _cache = cache;
        _sysCacheService = sysCacheService;
    }

    /// <summary>
    /// 分页查询会议人员表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询会议人员表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MeetingStaffOutput>> Page(PageMeetingStaffInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _meetingStaffRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.staff_code.Contains(input.Keyword) || u.staff_name.Contains(input.Keyword) || u.position.Contains(input.Keyword) || u.department.Contains(input.Keyword) || u.email.Contains(input.Keyword) || u.phone.Contains(input.Keyword) || u.avatar_url.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.staff_code), u => u.staff_code.Contains(input.staff_code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.staff_name), u => u.staff_name.Contains(input.staff_name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.position), u => u.position.Contains(input.position.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.department), u => u.department.Contains(input.department.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.email), u => u.email.Contains(input.email.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.phone), u => u.phone.Contains(input.phone.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.avatar_url), u => u.avatar_url.Contains(input.avatar_url.Trim()))
            .Select<MeetingStaffOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取会议人员表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取会议人员表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MeetingStaff> Detail([FromQuery] QueryByIdMeetingStaffInput input)
    {
        return await _meetingStaffRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加会议人员表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加会议人员表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMeetingStaffInput input)
    {
        // 使用事务确保数据一致性
        var result = await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            var entity = input.Adapt<MeetingStaff>();
            
            // 检查staff_code在本地数据库中的唯一性
            if (!string.IsNullOrWhiteSpace(entity.staff_code))
            {
                var existingStaff = await _meetingStaffRep.GetFirstAsync(u => u.staff_code == entity.staff_code && u.IsDelete == false);
                if (existingStaff != null)
                {
                    throw Oops.Oh($"员工编号 '{entity.staff_code}' 已存在，请使用其他编号");
                }
            }
            
            // 先同步到第三方平台
            var thirdPartyInput = new CreateMeetingStaffInput
            {
                Code = entity.staff_code,
                Name = entity.staff_name,
                Mobile = entity.phone,
                Company = entity.department,
                Position = entity.position,
                Field1 = entity.field1,
                Field2 = entity.field2,
                Field3 = entity.field3,
                Field4 = entity.field4,
                Field5 = entity.field5,
                Field6 = entity.field6,
                Description = entity.description
            };
            
            var thirdPartyId = await _greenDisplayService.CreateMeetingStaffAsync(thirdPartyInput);
            
            if (thirdPartyId <= 0)
            {
                var requestPath = "/admin-api/meeting/staff/create";
                var avatarUrl = GetFullAvatarUrl(entity.avatar_url);
                _logger.LogError("会议人员同步到第三方平台失败 - 姓名: {Name}, 员工编号: {StaffCode}, 请求路径: {RequestPath}, 头像URL: {AvatarUrl}", 
                    entity.staff_name, entity.staff_code, requestPath, avatarUrl);
                throw Oops.Oh("同步会议人员到第三方平台失败，可能是员工编号已存在或其他原因，操作已回滚");
            }
            
            // 第三方平台创建成功，回填staff_id并保存到本地数据库
            entity.staff_id = thirdPartyId;
            var insertResult = await _meetingStaffRep.InsertAsync(entity) ? entity.Id : 0;
            
            if (insertResult > 0)
            {
                var requestPathSuccess = "/admin-api/meeting/staff/create";
                var avatarUrlSuccess = GetFullAvatarUrl(entity.avatar_url);
                _logger.LogInformation("会议人员创建成功 - 本地ID: {LocalId}, 第三方ID: {ThirdPartyId}, 姓名: {Name}, 员工编号: {StaffCode}, 请求路径: {RequestPath}, 头像URL: {AvatarUrl}", 
                    entity.Id, thirdPartyId, entity.staff_name, entity.staff_code, requestPathSuccess, avatarUrlSuccess);
            }
            
            return insertResult;
        });
        
        return result.Data;
    }

    /// <summary>
    /// 更新会议人员表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新会议人员表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateMeetingStaffInput input)
    {
        // 使用事务确保数据一致性
        await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            var entity = input.Adapt<MeetingStaff>();
            
            // 检查staff_code在本地数据库中的唯一性（排除当前记录）
            if (!string.IsNullOrWhiteSpace(entity.staff_code))
            {
                var existingStaff = await _meetingStaffRep.GetFirstAsync(u => u.staff_code == entity.staff_code && u.Id != entity.Id && u.IsDelete == false);
                if (existingStaff != null)
                {
                    throw Oops.Oh($"员工编号 '{entity.staff_code}' 已存在，请使用其他编号");
                }
            }
            
            await _meetingStaffRep.AsUpdateable(entity)
            .IgnoreColumns(u => new {
                u.meeting_staff_status,
            })
            .ExecuteCommandAsync();
            
            // 同步到第三方平台
            var thirdPartyInput = new Admin.NET.Plugin.GreenDisplay.Service.UpdateMeetingStaffInput
            {
                Code = entity.staff_code,
                Name = entity.staff_name,
                Mobile = entity.phone,
                Company = entity.department,
                Position = entity.position,
                Field1 = entity.field1,
                Field2 = entity.field2,
                Field3 = entity.field3,
                Field4 = entity.field4,
                Field5 = entity.field5,
                Field6 = entity.field6,
                Description = entity.description
            };
            
            var syncResult = await _greenDisplayService.UpdateMeetingStaffAsync(thirdPartyInput);
            
            if (!syncResult)
            {
                var requestPath = "/admin-api/meeting/staff/update";
                var avatarUrl = GetFullAvatarUrl(entity.avatar_url);
                _logger.LogError("会议人员更新同步到第三方平台失败 - ID: {Id}, 姓名: {Name}, 请求路径: {RequestPath}, 头像URL: {AvatarUrl}", 
                    entity.Id, entity.staff_name, requestPath, avatarUrl);
                throw Oops.Oh("同步会议人员更新到第三方平台失败，操作已回滚");
            }
            
            var requestPathSuccess = "/admin-api/meeting/staff/update";
            var avatarUrlSuccess = GetFullAvatarUrl(entity.avatar_url);
            _logger.LogInformation("会议人员更新同步到第三方平台成功 - ID: {Id}, 姓名: {Name}, 请求路径: {RequestPath}, 头像URL: {AvatarUrl}", 
                entity.Id, entity.staff_name, requestPathSuccess, avatarUrlSuccess);
        });
    }

    /// <summary>
    /// 删除会议人员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除会议人员表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteMeetingStaffInput input)
    {
        var entity = await _meetingStaffRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        
        // 使用事务确保数据一致性
        var result = await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            // 先同步删除第三方平台数据
            if (!string.IsNullOrWhiteSpace(entity.staff_code) && entity.staff_id.HasValue)
            {
                var syncResult = await _greenDisplayService.DeleteMeetingStaffAsync(entity.staff_id.Value);
                if (!syncResult)
                {
                    _logger.LogError("会议人员删除同步到第三方平台失败 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}", 
                        entity.Id, entity.staff_code, entity.staff_name);
                    throw Oops.Oh("同步删除第三方平台数据失败");
                }
                else
                {
                    _logger.LogInformation("会议人员删除同步到第三方平台成功 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}", 
                        entity.Id, entity.staff_code, entity.staff_name);
                }
            }
            
            // 第三方删除成功后，再执行本地删除
            await _meetingStaffRep.FakeDeleteAsync(entity);   //假删除
            //await _meetingStaffRep.DeleteAsync(entity);   //真删除
            
            return true;
        });
        
        if (!result.IsSuccess)
        {
            throw Oops.Oh($"删除会议人员失败: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 批量删除会议人员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除会议人员表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteMeetingStaffInput> input)
    {
        var exp = Expressionable.Create<MeetingStaff>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _meetingStaffRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
        
        // 使用事务确保批量删除的数据一致性
        var result = await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            // 先同步删除第三方平台数据
            var failedStaffCodes = new List<string>();
            foreach (var entity in list.Where(x => !string.IsNullOrWhiteSpace(x.staff_code) && x.staff_id.HasValue))
            {
                var syncResult = await _greenDisplayService.DeleteMeetingStaffAsync(entity.staff_id.Value);
                if (!syncResult)
                {
                    failedStaffCodes.Add(entity.staff_code);
                    _logger.LogError("会议人员批量删除同步到第三方平台失败 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}", 
                        entity.Id, entity.staff_code, entity.staff_name);
                }
                else
                {
                    _logger.LogInformation("会议人员批量删除同步到第三方平台成功 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}", 
                        entity.Id, entity.staff_code, entity.staff_name);
                }
            }
            
            // 如果有第三方删除失败的情况，抛出异常回滚事务
            if (failedStaffCodes.Any())
            {
                var errorMessage = $"部分会议人员同步删除到第三方平台失败，员工编号: {string.Join(", ", failedStaffCodes)}";
                _logger.LogError(errorMessage);
                throw Oops.Oh(errorMessage);
            }
            
            // 所有第三方删除成功后，再执行本地批量删除
            var deleteResult = await _meetingStaffRep.FakeDeleteAsync(list);   //假删除
            //var deleteResult = await _meetingStaffRep.DeleteAsync(list);   //真删除
            
            return deleteResult;
        });
        
        if (!result.IsSuccess)
        {
            throw Oops.Oh($"批量删除会议人员失败: {result.ErrorMessage}");
        }
        
        return result.Data;
    }
    
    /// <summary>
    /// 导出会议人员表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出会议人员表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageMeetingStaffInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportMeetingStaffOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "会议人员表导出记录");
    }
    
    /// <summary>
    /// 下载会议人员表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载会议人员表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportMeetingStaffOutput>(), "会议人员表导入模板");
    }
    
    private static readonly object _meetingStaffImportLock = new object();
    
    /// <summary>
    /// 导入会议人员表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入会议人员表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public async Task<IActionResult> ImportData([Required] IFormFile file)
    {
        
    }
    
    
    /// <summary>
    /// 获取完整的头像URL（拼接域名）
    /// </summary>
    /// <param name="avatarUrl">原始头像URL</param>
    /// <returns>完整的头像URL</returns>
    private string GetFullAvatarUrl(string avatarUrl)
    {
        if (string.IsNullOrWhiteSpace(avatarUrl))
        {
            return string.Empty;
        }
        
        // 如果已经是完整URL，直接返回
        if (avatarUrl.StartsWith("http://") || avatarUrl.StartsWith("https://"))
        {
            return avatarUrl;
        }
        
        // 从配置中获取基础域名
        var baseUrl = _configuration["AppSettings:FileBaseUrl"] ?? _configuration["GreenDisplay:BaseUrl"] ?? "http://localhost";
        
        // 确保baseUrl以/结尾，avatarUrl不以/开头
        if (!baseUrl.EndsWith("/"))
        {
            baseUrl += "/";
        }
        
        if (avatarUrl.StartsWith("/"))
        {
            avatarUrl = avatarUrl.Substring(1);
        }
        
        return baseUrl + avatarUrl;
    }
}
