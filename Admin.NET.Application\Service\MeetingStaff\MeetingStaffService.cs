﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
namespace Admin.NET.Application;

/// <summary>
/// 会议人员表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class MeetingStaffService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MeetingStaff> _meetingStaffRep;

    public MeetingStaffService(SqlSugarRepository<MeetingStaff> meetingStaffRep)
    {
        _meetingStaffRep = meetingStaffRep;
    }

    /// <summary>
    /// 分页查询会议人员表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询会议人员表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MeetingStaffOutput>> Page(PageMeetingStaffInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _meetingStaffRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.staff_code.Contains(input.Keyword) || u.staff_name.Contains(input.Keyword) || u.position.Contains(input.Keyword) || u.department.Contains(input.Keyword) || u.email.Contains(input.Keyword) || u.phone.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.staff_code), u => u.staff_code.Contains(input.staff_code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.staff_name), u => u.staff_name.Contains(input.staff_name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.position), u => u.position.Contains(input.position.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.department), u => u.department.Contains(input.department.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.email), u => u.email.Contains(input.email.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.phone), u => u.phone.Contains(input.phone.Trim()))
            .WhereIF(input.meeting_staff_status != null, u => u.meeting_staff_status == input.meeting_staff_status)
            .Select<MeetingStaffOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取会议人员表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取会议人员表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MeetingStaff> Detail([FromQuery] QueryByIdMeetingStaffInput input)
    {
        return await _meetingStaffRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加会议人员表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加会议人员表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMeetingStaffInput input)
    {
        var entity = input.Adapt<MeetingStaff>();
        return await _meetingStaffRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新会议人员表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新会议人员表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateMeetingStaffInput input)
    {
        var entity = input.Adapt<MeetingStaff>();
        await _meetingStaffRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除会议人员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除会议人员表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteMeetingStaffInput input)
    {
        var entity = await _meetingStaffRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _meetingStaffRep.FakeDeleteAsync(entity);   //假删除
        //await _meetingStaffRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除会议人员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除会议人员表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteMeetingStaffInput> input)
    {
        var exp = Expressionable.Create<MeetingStaff>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _meetingStaffRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _meetingStaffRep.FakeDeleteAsync(list);   //假删除
        //return await _meetingStaffRep.DeleteAsync(list);   //真删除
    }
}
