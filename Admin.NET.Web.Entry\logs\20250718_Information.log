fail: 2025-07-18 09:44:19.8392786 +08:00 Friday L Admin.NET.Application.MeetingStaffService[0] #19 '00-3ef71afdaa9ff3d0f52e0f773facba6c-24723bdd159dfd3f-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      会议人员更新同步到第三方平台失败 - ID: 698687391182917, 姓名: rrere, 请求路径: /admin-api/meeting/staff/update, 头像URL: 
fail: 2025-07-18 10:16:07.0113401 +08:00 Friday L System.Logging.LoggingMonitor[0] #22 '00-572e12ce7a0e029c623f8af2a6ee7dd9-3b03d288382f8cc7-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.BatchDelete
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       BatchDelete
      ┣ 显示名称：                       批量删除会议人员表
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: batchDelete
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/batchDelete
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   55519
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-572e12ce7a0e029c623f8af2a6ee7dd9-3b03d288382f8cc7-00
      ┣ 服务线程 ID：                    #42
      ┣ 执行耗时：                       386ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   application/json
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 51
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   application/json
      ┣ 
      ┣ input (List`1)：                 [{"id":698937454440517},{"id":698687391182917}]
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<System.Int32>
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Nullable object must have a value.
      ┣ 错误堆栈：                       at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 10:16:15.0209270 +08:00 Friday L System.Logging.LoggingMonitor[0] #9 '00-3f25263515c6a5a0802824e2368d8768-cf2c1359fbbd7b0d-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.BatchDelete
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       BatchDelete
      ┣ 显示名称：                       批量删除会议人员表
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: batchDelete
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/batchDelete
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   55519
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-3f25263515c6a5a0802824e2368d8768-cf2c1359fbbd7b0d-00
      ┣ 服务线程 ID：                    #9
      ┣ 执行耗时：                       104ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   application/json
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 51
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   application/json
      ┣ 
      ┣ input (List`1)：                 [{"id":698937454440517},{"id":698687391182917}]
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<System.Int32>
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Nullable object must have a value.
      ┣ 错误堆栈：                       at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 10:17:51.4087935 +08:00 Friday L System.Logging.LoggingMonitor[0] #41 '00-2e1843840c1746ceac41ce63eb2b4a0b-973301fadaf4d2ea-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.BatchDelete
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       BatchDelete
      ┣ 显示名称：                       批量删除会议人员表
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: batchDelete
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/batchDelete
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   55519
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-2e1843840c1746ceac41ce63eb2b4a0b-973301fadaf4d2ea-00
      ┣ 服务线程 ID：                    #41
      ┣ 执行耗时：                       78118ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   application/json
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 51
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   application/json
      ┣ 
      ┣ input (List`1)：                 [{"id":698937454440517},{"id":698687391182917}]
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<System.Int32>
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Nullable object must have a value.
      ┣ 错误堆栈：                       at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.BatchDelete(List`1 input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 263
         at lambda_method446(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 10:24:43.2932934 +08:00 Friday L System.Logging.LoggingMonitor[0] #51 '00-eae6869a2523f3397c92b6dc5b9139ed-95f804aeb8c6a114-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.Delete
      ┣ 
      ┣ 控制器名称：                             MeetingStaffService
      ┣ 操作名称：                               Delete
      ┣ 显示名称：                               删除会议人员表
      ┣ 路由信息：                               [area]: ; [controller]: meetingStaff; [action]: delete
      ┣ 请求方式：                               POST
      ┣ 请求地址：                               http://localhost:5005/api/meetingStaff/delete
      ┣ HTTP 协议：                              HTTP/1.1
      ┣ 来源地址：                               http://localhost:8888/
      ┣ 请求端源：                               client
      ┣ 浏览器标识：                             Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                         zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                         *******
      ┣ 客户端源端口：                           55520
      ┣ 服务端 IP 地址：                         *******
      ┣ 服务端源端口：                           5005
      ┣ 客户端连接 ID：                          00-eae6869a2523f3397c92b6dc5b9139ed-95f804aeb8c6a114-00
      ┣ 服务线程 ID：                            #51
      ┣ 执行耗时：                               79ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                                 
      ┣ 响应端：                                 
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                               Microsoft Windows 10.0.26100
      ┣ 系统架构：                               X64
      ┣ 基础框架：                               Furion.Pure v4.9.7.93
      ┣ .NET 架构：                              .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                           http://[::]:5005
      ┣ 运行环境：                               Development
      ┣ 启动程序集：                             Admin.NET.Web.Entry
      ┣ 进程名称：                               Admin.NET.Web.Entry
      ┣ 托管程序：                               Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                              Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：                     *************
      ┣ TenantId (integer64)：                   *************
      ┣ Account (string)：                       superadmin
      ┣ RealName (string)：                      超级管理员
      ┣ AccountType (integer32)：                999
      ┣ OrgId (integer32)：                      0
      ┣ OrgName (JSON_NULL)：                    
      ┣ OrgType (JSON_NULL)：                    
      ┣ iat (integer64)：                        ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                        ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                        ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                           Admin.NET
      ┣ aud (string)：                           Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                                 application/json, text/plain, */*
      ┣ Connection：                             keep-alive
      ┣ Host：                                   localhost:5005
      ┣ User-Agent：                             Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                        gzip, deflate, br, zstd
      ┣ Accept-Language：                        zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                          Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                           application/json
      ┣ Origin：                                 http://localhost:8888
      ┣ Referer：                                http://localhost:8888/
      ┣ Content-Length：                         24
      ┣ sec-ch-ua-platform：                     "Windows"
      ┣ sec-ch-ua：                              "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：                       ?0
      ┣ Sec-Fetch-Site：                         same-site
      ┣ Sec-Fetch-Mode：                         cors
      ┣ Sec-Fetch-Dest：                         empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                           application/json
      ┣ 
      ┣ input (DeleteMeetingStaffInput)：        {"id":698942277550149}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                         200
      ┣ 原始类型：                               System.Threading.Tasks.Task
      ┣ 最终类型：                               
      ┣ 最终返回值：                             null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                                   System.InvalidOperationException
      ┣ 消息：                                   Nullable object must have a value.
      ┣ 错误堆栈：                               at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.Delete(DeleteMeetingStaffInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 230
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Nullable object must have a value.
         at System.Nullable`1.get_Value()
         at Admin.NET.Application.MeetingStaffService.Delete(DeleteMeetingStaffInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 230
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:16:31.0167591 +08:00 Friday L System.Logging.UnitOfWork[0] #19 '00-d1ddddcf1bf269cc804d074261907bcd-34bed57fb874e0c0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesSync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 40
      --- End of stack trace from previous location ---
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 133
         at MySqlConnector.MySqlDataReader.CreateAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 468
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:16:32.2927258 +08:00 Friday L System.Logging.LoggingMonitor[0] #19 '00-d1ddddcf1bf269cc804d074261907bcd-34bed57fb874e0c0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   60897
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-d1ddddcf1bf269cc804d074261907bcd-34bed57fb874e0c0-00
      ┣ 服务线程 ID：                    #19
      ┣ 执行耗时：                       544373ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryBHEDbSMfBMJt7urV
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 11227
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryBHEDbSMfBMJt7urV
      ┣ 
      ┣ file (IFormFile)：               [name]: 会议人员表导入模板-2025-07-18_105704.xlsx; [size]: 11KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           MySqlConnector.MySqlException
      ┣ 消息：                           Failed to read the result set.
      ┣ 错误堆栈：                       at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 133
         at MySqlConnector.MySqlDataReader.CreateAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 468
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10054): 远程主机强迫关闭了一个现有的连接。
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesSync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 40
      --- End of stack trace from previous location ---
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 133
         at MySqlConnector.MySqlDataReader.CreateAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 468
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:24:32.3628126 +08:00 Friday L System.Logging.StringLogging[0] #25 '00-3a47beb4522794349471aadce9212827-7f4f6e739a35adee-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 11:24:32——错误SQL】
      
      [Sql]:INSERT INTO `SysFile`  
                 (`Provider`,`BucketName`,`FileName`,`Suffix`,`FilePath`,`SizeKb`,`SizeInfo`,`Url`,`FileMd5`,`FileType`,`FileAlias`,`IsPublic`,`DataId`,`TenantId`,`OrgId`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`)
           VALUES
                 (@Provider,@BucketName,@FileName,@Suffix,@FilePath,@SizeKb,@SizeInfo,@Url,@FileMd5,@FileType,@FileAlias,@IsPublic,@DataId,@TenantId,@OrgId,@CreateTime,@UpdateTime,@CreateUserId,@CreateUserName,@UpdateUserId,@UpdateUserName,@Id) ; 
      [Pars]:
      [Name]:@Provider [Value]: [Type]:String    
      [Name]:@BucketName [Value]:Local [Type]:String    
      [Name]:@FileName [Value]:会议人员表导入模板-2025-07-18_105704 [Type]:String    
      [Name]:@Suffix [Value]:.xlsx [Type]:String    
      [Name]:@FilePath [Value]:upload/2025/07/18 [Type]:String    
      [Name]:@SizeKb [Value]:10 [Type]:Int64    
      [Name]:@SizeInfo [Value]: [Type]:String    
      [Name]:@Url [Value]:upload/2025/07/18/698957169053765.xlsx [Type]:String    
      [Name]:@FileMd5 [Value]: [Type]:String    
      [Name]:@FileType [Value]: [Type]:String    
      [Name]:@FileAlias [Value]: [Type]:String    
      [Name]:@IsPublic [Value]:False [Type]:Boolean    
      [Name]:@DataId [Value]: [Type]:Int64    
      [Name]:@TenantId [Value]:************* [Type]:Int64    
      [Name]:@OrgId [Value]:0 [Type]:Int64    
      [Name]:@CreateTime [Value]:2025-7-18 11:24:02 [Type]:DateTime    
      [Name]:@UpdateTime [Value]: [Type]:DateTime    
      [Name]:@CreateUserId [Value]:************* [Type]:Int64    
      [Name]:@CreateUserName [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:698957169053765 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: The Command Timeout expired before the operation completed.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:25:25.9523303 +08:00 Friday L System.Logging.StringLogging[0] #42 '00-c75ca629e06339e093256c806d9ef0cc-9b1f6b543267554b-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 11:25:25——错误SQL】
      
      [Sql]:INSERT INTO `SysFile`  
                 (`Provider`,`BucketName`,`FileName`,`Suffix`,`FilePath`,`SizeKb`,`SizeInfo`,`Url`,`FileMd5`,`FileType`,`FileAlias`,`IsPublic`,`DataId`,`TenantId`,`OrgId`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`)
           VALUES
                 (@Provider,@BucketName,@FileName,@Suffix,@FilePath,@SizeKb,@SizeInfo,@Url,@FileMd5,@FileType,@FileAlias,@IsPublic,@DataId,@TenantId,@OrgId,@CreateTime,@UpdateTime,@CreateUserId,@CreateUserName,@UpdateUserId,@UpdateUserName,@Id) ; 
      [Pars]:
      [Name]:@Provider [Value]: [Type]:String    
      [Name]:@BucketName [Value]:Local [Type]:String    
      [Name]:@FileName [Value]:会议人员表导入模板-2025-07-18_105704 [Type]:String    
      [Name]:@Suffix [Value]:.xlsx [Type]:String    
      [Name]:@FilePath [Value]:upload/2025/07/18 [Type]:String    
      [Name]:@SizeKb [Value]:10 [Type]:Int64    
      [Name]:@SizeInfo [Value]: [Type]:String    
      [Name]:@Url [Value]:upload/2025/07/18/698957387808837.xlsx [Type]:String    
      [Name]:@FileMd5 [Value]: [Type]:String    
      [Name]:@FileType [Value]: [Type]:String    
      [Name]:@FileAlias [Value]: [Type]:String    
      [Name]:@IsPublic [Value]:False [Type]:Boolean    
      [Name]:@DataId [Value]: [Type]:Int64    
      [Name]:@TenantId [Value]:************* [Type]:Int64    
      [Name]:@OrgId [Value]:0 [Type]:Int64    
      [Name]:@CreateTime [Value]:2025-7-18 11:24:55 [Type]:DateTime    
      [Name]:@UpdateTime [Value]: [Type]:DateTime    
      [Name]:@CreateUserId [Value]:************* [Type]:Int64    
      [Name]:@CreateUserName [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:698957387808837 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: The Command Timeout expired before the operation completed.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:36:47.6340177 +08:00 Friday L System.Logging.StringLogging[0] #43 '00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-18 11:36:47——错误SQL】
      
      [Sql]:INSERT INTO `SysFile`  
                 (`Provider`,`BucketName`,`FileName`,`Suffix`,`FilePath`,`SizeKb`,`SizeInfo`,`Url`,`FileMd5`,`FileType`,`FileAlias`,`IsPublic`,`DataId`,`TenantId`,`OrgId`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`)
           VALUES
                 (@Provider,@BucketName,@FileName,@Suffix,@FilePath,@SizeKb,@SizeInfo,@Url,@FileMd5,@FileType,@FileAlias,@IsPublic,@DataId,@TenantId,@OrgId,@CreateTime,@UpdateTime,@CreateUserId,@CreateUserName,@UpdateUserId,@UpdateUserName,@Id) ; 
      [Pars]:
      [Name]:@Provider [Value]: [Type]:String    
      [Name]:@BucketName [Value]:Local [Type]:String    
      [Name]:@FileName [Value]:会议人员表导入模板-2025-07-18_105704 [Type]:String    
      [Name]:@Suffix [Value]:.xlsx [Type]:String    
      [Name]:@FilePath [Value]:upload/2025/07/18 [Type]:String    
      [Name]:@SizeKb [Value]:10 [Type]:Int64    
      [Name]:@SizeInfo [Value]: [Type]:String    
      [Name]:@Url [Value]:upload/2025/07/18/698959067435077.xlsx [Type]:String    
      [Name]:@FileMd5 [Value]: [Type]:String    
      [Name]:@FileType [Value]: [Type]:String    
      [Name]:@FileAlias [Value]: [Type]:String    
      [Name]:@IsPublic [Value]:False [Type]:Boolean    
      [Name]:@DataId [Value]: [Type]:Int64    
      [Name]:@TenantId [Value]:************* [Type]:Int64    
      [Name]:@OrgId [Value]:0 [Type]:Int64    
      [Name]:@CreateTime [Value]:2025-7-18 11:31:45 [Type]:DateTime    
      [Name]:@UpdateTime [Value]: [Type]:DateTime    
      [Name]:@CreateUserId [Value]:************* [Type]:Int64    
      [Name]:@CreateUserName [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:698959067435077 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: The Command Timeout expired before the operation completed.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:37:13.7077404 +08:00 Friday L System.Logging.UnitOfWork[0] #10 '00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Connection must be Open; current state is Closed
         at MySqlConnector.MySqlConnection.get_Session() in /_/src/MySqlConnector/MySqlConnection.cs:line 745
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 32
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-18 11:37:13.8987675 +08:00 Friday L System.Logging.LoggingMonitor[0] #10 '00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   65164
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-9262221a3ab9ec3f3b587dfcb95d1acd-8a56870aec8ade1a-00
      ┣ 服务线程 ID：                    #10
      ┣ 执行耗时：                       329155ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ nbf (integer64)：                ********** (2025-07-18 09:39:59:0000(+08:00) Friday L)
      ┣ exp (integer64)：                ********** (2025-07-25 09:39:59:0000(+08:00) Friday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.HLCteRJEeZA7ab_0K8Qhv3G0nGDCSpPI_qGuvDA_vNA
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryU74BoTuVoiAajbk4
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 11326
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryU74BoTuVoiAajbk4
      ┣ 
      ┣ file (IFormFile)：               [name]: 会议人员表导入模板-2025-07-18_105704.xlsx; [size]: 11KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           Connection must be Open; current state is Closed
      ┣ 错误堆栈：                       at MySqlConnector.MySqlConnection.get_Session() in /_/src/MySqlConnector/MySqlConnection.cs:line 745
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 32
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Connection must be Open; current state is Closed
         at MySqlConnector.MySqlConnection.get_Session() in /_/src/MySqlConnector/MySqlConnection.cs:line 745
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 32
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
