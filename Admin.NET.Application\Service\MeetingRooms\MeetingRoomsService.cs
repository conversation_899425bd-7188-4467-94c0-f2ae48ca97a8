// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
using Admin.NET.Plugin.GreenDisplay.Service;
using Microsoft.Extensions.Logging;
namespace Admin.NET.Application;

/// <summary>
/// 会议室表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class MeetingRoomsService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MeetingRooms> _meetingRoomsRep;
    private readonly GreenDisplayService _greenDisplayService;
    private readonly ILogger<MeetingRoomsService> _logger;


    public MeetingRoomsService(SqlSugarRepository<MeetingRooms> meetingRoomsRep, GreenDisplayService greenDisplayService, ILogger<MeetingRoomsService> logger)
    {
        _meetingRoomsRep = meetingRoomsRep;
        _greenDisplayService = greenDisplayService;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询会议室表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询会议室表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MeetingRoomsOutput>> Page(PageMeetingRoomsInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _meetingRoomsRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.room_name.Contains(input.Keyword) || u.room_code.Contains(input.Keyword) || u.room_location.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.room_name), u => u.room_name.Contains(input.room_name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.room_code), u => u.room_code.Contains(input.room_code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.room_location), u => u.room_location.Contains(input.room_location.Trim())) 
            .WhereIF(input.capacity != null, u => u.capacity == input.capacity)
            .WhereIF(input.meeting_status != null, u => u.meeting_status == input.meeting_status)
            .Select<MeetingRoomsOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取会议室表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取会议室表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MeetingRooms> Detail([FromQuery] QueryByIdMeetingRoomsInput input)
    {
        return await _meetingRoomsRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加会议室表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns>返回会议室ID，0表示失败</returns>
    [DisplayName("增加会议室表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMeetingRoomsInput input)
    {
        var entity = input.Adapt<MeetingRooms>();
        //检验会议室编号、会议室名称唯一性
        var isUnique = await ValidateUniquenessAsync(input.room_code, input.room_name);
        if (!isUnique)
        {
            _logger.LogWarning("会议室编号或名称已存在，添加失败");
            return 0;
        }
        
        // 使用事务确保数据一致性
        try
        {
            var result = await _meetingRoomsRep.AsTenant().UseTranAsync(async () =>
            {
                // 先创建第三方平台会议室
                var createMeetingRoomInput = new CreateMeetingRoomInput()
                {
                    Name = entity.room_name
                };
                
                var thirdPartyId = await _greenDisplayService.CreateMeetingRoomAsync(createMeetingRoomInput);
                if (thirdPartyId <= 0)
                {
                    _logger.LogError("第三方平台创建会议室失败，会议室名称: {RoomName}", entity.room_name);
                    throw new Exception("第三方平台创建失败");
                }
                
                // 将第三方平台ID保存到实体中
                entity.room_id = thirdPartyId;
                
                // 插入本地数据库
                var insertResult = await _meetingRoomsRep.InsertAsync(entity);
                if (!insertResult)
                {
                    _logger.LogError("会议室数据插入本地数据库失败，第三方平台ID: {ThirdPartyId}", thirdPartyId);
                    throw new Exception("本地数据库插入失败");
                }
                
                _logger.LogInformation("会议室创建成功，本地ID: {RoomId}, 第三方平台ID: {ThirdPartyId}, 名称: {RoomName}", entity.Id, thirdPartyId, entity.room_name);
                return entity.Id;
            });
            return result.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加会议室失败，会议室名称: {RoomName}", entity.room_name);
            return 0; // 返回0表示失败，不抛出异常
        }
    }

    /// <summary>
    /// 更新会议室表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新会议室表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task<bool> Update(UpdateMeetingRoomsInput input)
    {
        //检验会议室编号、会议室名称唯一性（排除当前记录）
        var isUnique = await ValidateUniquenessAsync(input.room_code, input.room_name, input.Id);
        if (!isUnique)
        {
            _logger.LogWarning("会议室编号或名称已存在，编号: {RoomCode}, 名称: {RoomName}", input.room_code, input.room_name);
            return false;
        }
        
        var entity = input.Adapt<MeetingRooms>();
        var updateResultEntity = await _meetingRoomsRep.GetFirstAsync(u => u.Id == entity.Id);
        entity.room_id = updateResultEntity.room_id;
        // 使用事务确保数据一致性
        try
        {
            var result = await _meetingRoomsRep.AsTenant().UseTranAsync(async () =>
            {
                // 先更新本地数据库
                var updateResult = await _meetingRoomsRep.AsUpdateable(entity).ExecuteCommandAsync();
                if (updateResult <= 0)
                {
                    _logger.LogError("会议室数据更新本地数据库失败，会议室ID: {RoomId}", entity.Id);
                    throw new Exception("本地数据库更新失败");
                }
              
                // 同步到第三方平台
                try
                {
                    // 检查room_id是否存在
                    if (!updateResultEntity.room_id.HasValue)
                    {
                        _logger.LogError("会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: {RoomId}", entity.Id);
                        throw new Exception("第三方平台ID为空，无法同步");
                    }
                    
                    var updateMeetingRoomInput = new UpdateMeetingRoomInput()
                    {
                        Id = updateResultEntity.room_id.Value,
                        Name = updateResultEntity.room_name
                    };
                    
                    var thirdPartyResult = await _greenDisplayService.UpdateMeetingRoomAsync(updateMeetingRoomInput);
                    if (!thirdPartyResult)
                    {
                        _logger.LogError("第三方平台更新会议室失败，会议室ID: {RoomId}, 名称: {RoomName}", entity.Id, entity.room_name);
                        throw new Exception("第三方平台同步失败");
                    }
                    
                    _logger.LogInformation("会议室更新成功，ID: {RoomId}, 名称: {RoomName}", entity.Id, entity.room_name);
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "第三方平台同步失败，会议室ID: {RoomId}, 名称: {RoomName}", entity.Id, entity.room_name);
                    throw; // 重新抛出异常以触发事务回滚
                }
            });
            
            if (!result.IsSuccess)
            {
                _logger.LogError("更新会议室事务失败，错误信息: {ErrorMessage}", result.ErrorMessage);
                return false;
            }
            
            return result.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新会议室失败，会议室ID: {RoomId}, 名称: {RoomName}", entity.Id, entity.room_name);
            return false;
        }
    }

    /// <summary>
    /// 删除会议室表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除会议室表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task<bool> Delete(DeleteMeetingRoomsInput input)
    {
        var entity = await _meetingRoomsRep.GetFirstAsync(u => u.Id == input.Id);
        if (entity == null)
        {
            _logger.LogWarning("要删除的会议室不存在，ID: {RoomId}", input.Id);
            return false;
        }
        
        // 使用事务确保数据一致性
        try
        {
            var result = await _meetingRoomsRep.AsTenant().UseTranAsync(async () =>
            {
                // 先删除本地数据库
                var deleteResult = await _meetingRoomsRep.FakeDeleteAsync(entity);   //假删除
                if (deleteResult <= 0)
                {
                    _logger.LogError("会议室数据删除本地数据库失败，会议室ID: {RoomId}", entity.Id);
                    throw new Exception("本地数据库删除失败");
                }
                
                // 同步到第三方平台
                try
                {
                    // 检查room_id是否存在
                    if (!entity.room_id.HasValue)
                    {
                        _logger.LogError("会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: {RoomId}", entity.Id);
                        throw new Exception("第三方平台ID为空，无法同步");
                    }
                    
                    var thirdPartyResult = await _greenDisplayService.DeleteMeetingRoomAsync(entity.room_id.Value);
                    if (!thirdPartyResult)
                    {
                        _logger.LogError("第三方平台删除会议室失败，会议室ID: {RoomId}, 第三方ID: {ThirdPartyId}, 名称: {RoomName}", entity.Id, entity.room_id.Value, entity.room_name);
                        throw new Exception("第三方平台同步失败");
                    }
                    
                    _logger.LogInformation("会议室删除成功，ID: {RoomId}, 名称: {RoomName}", entity.Id, entity.room_name);
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "第三方平台同步失败，会议室ID: {RoomId}, 名称: {RoomName}", entity.Id, entity.room_name);
                    throw; // 重新抛出异常以触发事务回滚
                }
            });
            
            if (!result.IsSuccess)
            {
                _logger.LogError("删除会议室事务失败，错误信息: {ErrorMessage}", result.ErrorMessage);
                return false;
            }
            
            return result.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除会议室失败，会议室ID: {RoomId}, 名称: {RoomName}", entity.Id, entity.room_name);
            return false;
        }
    }

    /// <summary>
    /// 批量删除会议室表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除会议室表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<bool> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteMeetingRoomsInput> input)
    {
        if (input == null || !input.Any())
        {
            _logger.LogWarning("批量删除会议室列表为空");
            return false;
        }
        
        var exp = Expressionable.Create<MeetingRooms>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _meetingRoomsRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
        
        if (!list.Any())
        {
            _logger.LogWarning("要删除的会议室列表为空");
            return false;
        }
        
        // 使用事务确保数据一致性
        try
        {
            var result = await _meetingRoomsRep.AsTenant().UseTranAsync(async () =>
            {
                // 先删除本地数据库
                var deleteResult = await _meetingRoomsRep.FakeDeleteAsync(list);   //假删除
                if (deleteResult <= 0)
                {
                    _logger.LogError("批量删除会议室本地数据库失败，删除数量: {Count}", list.Count);
                    throw new Exception("本地数据库批量删除失败");
                }
                
                // 同步到第三方平台
                var failedIds = new List<long>();
                foreach (var entity in list)
                {
                    try
                    {
                        // 检查room_id是否存在
                        if (!entity.room_id.HasValue)
                        {
                            failedIds.Add(entity.Id);
                            _logger.LogError("会议室的第三方平台ID为空，无法同步到第三方平台，会议室ID: {RoomId}", entity.Id);
                            continue;
                        }
                        
                        var thirdPartyResult = await _greenDisplayService.DeleteMeetingRoomAsync(entity.room_id.Value);
                        if (!thirdPartyResult)
                        {
                            failedIds.Add(entity.Id);
                            _logger.LogError("第三方平台删除会议室失败，会议室ID: {RoomId}, 第三方ID: {ThirdPartyId}, 名称: {RoomName}", entity.Id, entity.room_id.Value, entity.room_name);
                        }
                    }
                    catch (Exception ex)
                    {
                        failedIds.Add(entity.Id);
                        _logger.LogError(ex, "第三方平台删除会议室异常，会议室ID: {RoomId}, 第三方ID: {ThirdPartyId}, 名称: {RoomName}", entity.Id, entity.room_id?.ToString() ?? "null", entity.room_name);
                    }
                }
                
                if (failedIds.Any())
                {
                    _logger.LogError("第三方平台批量删除部分失败，失败ID列表: {FailedIds}", string.Join(",", failedIds));
                    throw new Exception("第三方平台批量同步失败");
                }
                
                _logger.LogInformation("批量删除会议室成功，删除数量: {Count}", list.Count);
                return true;
            });
            
            if (!result.IsSuccess)
            {
                _logger.LogError("批量删除会议室事务失败，错误信息: {ErrorMessage}", result.ErrorMessage);
                return false;
            }
            
            return result.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量删除会议室失败，删除数量: {Count}", list.Count);
            return false;
        }
    }

    /// <summary>
    /// 检验会议室编号和名称的唯一性
    /// </summary>
    /// <param name="roomCode">房间编号</param>
    /// <param name="roomName">房间名称</param>
    /// <param name="excludeId">排除的ID（更新时使用）</param>
    /// <returns>返回验证结果，true表示唯一，false表示重复</returns>
    private async Task<bool> ValidateUniquenessAsync(string? roomCode, string? roomName, long? excludeId = null)
    {
        if (!string.IsNullOrWhiteSpace(roomCode))
        {
            var codeExists = await _meetingRoomsRep.AsQueryable()
                .Where(x => x.room_code == roomCode.Trim())
                .WhereIF(excludeId.HasValue, x => x.Id != excludeId.Value)
                .AnyAsync();
            
            if (codeExists)
            {
                _logger.LogWarning("房间编号 '{RoomCode}' 已存在", roomCode);
                return false;
            }
        }
        
        if (!string.IsNullOrWhiteSpace(roomName))
        {
            var nameExists = await _meetingRoomsRep.AsQueryable()
                .Where(x => x.room_name == roomName.Trim())
                .WhereIF(excludeId.HasValue, x => x.Id != excludeId.Value)
                .AnyAsync();
            
            if (nameExists)
            {
                _logger.LogWarning("房间名称 '{RoomName}' 已存在", roomName);
                return false;
            }
        }
        
        return true;
    }
}
